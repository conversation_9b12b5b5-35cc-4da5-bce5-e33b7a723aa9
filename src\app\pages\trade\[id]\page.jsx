"use client";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSearchParams, useParams } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import styles from "../search_results.module.css";
import Modal from "react-modal";
import Chat from "../../../components/Chat/page";

import "react-toastify/dist/ReactToastify.css";
import TradeTimer from "../../../components/TradeTimer/page";
import TradeStepper from "../../../components/TradeStepper/page";
import DisputeTicket from "../../../components/TradepageModals/DisputeTicket/page";
import TradeReviewModal from "../../../components/TradeReviewModal/page";
import { useSSE } from "@/app/context/SSEContext";
import Layout from "../../../components/Layout/page";
// New extracted components
import TradeHeader from "../../../components/TradePageComponents/TradeHeader";
import TradeInstructions from "../../../components/TradePageComponents/TradeInstructions";
import PaymentInfo from "../../../components/TradePageComponents/PaymentInfo";
import TradeActions from "../../../components/TradePageComponents/TradeActions";
import WaitingModal from "../../../components/TradePageComponents/WaitingModal";
const page = () => {
  const useQueryParams = useSearchParams();
  const router = useRouter();
  const { event, remflowEvent } = useSSE();

  const authTokenRef = useRef(null);

  const params = useParams();
  const [tradeData, setTradeData] = useState("");
  const [timeLimit, setTimeLimit] = useState("");
  const [btnNameUser, setBtnNameUser] = useState("");
  const [btnNamePeer, setBtnNamePeer] = useState("");
  const [userTimeLine, setUserTimeLine] = useState("");
  const [peerTimeLine, setPeerTimeLine] = useState("");
  const [tradeDecision, setTradeDecision] = useState("");
  const [payoutDetails, setPayoutDetails] = useState();
  const [activeStep, setActiveStep] = useState(0);

  console.log("tradeData", tradeData);
  console.log("tradeData?.flag", tradeData?.flag);
  console.log("tradeData?.data", tradeData?.data);
  console.log("activeStep123", activeStep);

  const orderNumber = params.id;
  const passedType = useQueryParams.get("type");
  console.log("increaseTimer", remflowEvent);

  useEffect(() => {
    // Only process remflowEvent if it has meaningful data
    if (!remflowEvent || !remflowEvent.flag) return;

    if (remflowEvent.flag === "user") {
      setActiveStep(Number(remflowEvent.stepper));
      setBtnNameUser(remflowEvent.button);
      setUserTimeLine(remflowEvent.time_line_state);
      // Toast notifications are handled in SSEContext
    } else if (remflowEvent.flag === "peer") {
      setActiveStep(Number(remflowEvent.stepper));
      setBtnNamePeer(remflowEvent.button);
      setPeerTimeLine(remflowEvent.time_line_state);
      // Toast notifications are handled in SSEContext
    }

    // Hide waiting modal when trade is accepted (stepper moves from 0 to 1+)
    if (remflowEvent.stepper && Number(remflowEvent.stepper) >= 1) {
      setShowWaitingModal(false);
    }
  }, [remflowEvent]);

  const getPeerDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/peer/data/?order_id=${orderNumber}`
      );
      console.log("resqq", res);
      setPeerTimeLine(res.data.data.time_line_state);
      setBtnNamePeer(res.data.data.button);
      setActiveStep(Number(res.data.data.stepper));
      // Messages will come via SSE - no duplicate toast needed
    } catch (error) {
      console.error("resqq", error);
    }
  };
  const getUserDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/user/data/?order_id=${orderNumber}`
      );
      console.log("resqqq", res);
      setBtnNameUser(res.data.data.button);
      setUserTimeLine(res.data.data.time_line_state);
      setActiveStep(Number(res.data.data.stepper));
      // Messages will come via SSE - no duplicate toast needed
    } catch (error) {
      console.error("resqqqq", error);
    }
  };

  useEffect(() => {
    if (tradeData?.flag !== "user") {
      getPeerDetailsByOrderId();
    } else if (tradeData?.flag === "user") {
      getUserDetailsByOrderId();
    }
  }, [tradeData?.flag]);

  let username1;
  if (typeof window !== "undefined") {
    username1 = localStorage.getItem("userName");
  }

  // Note: Removed fetchPeerDetailsApi as it was calling undefined state setters

  const [modalIsOpen, setIsOpen] = useState(false);
  const [modalIsOpen2, setIsOpen2] = useState(false);
  const [modalIsOpen3, setIsOpen3] = useState(false);
  const [modalIsOpen4, setIsOpen4] = useState(false);
  const [modalIsOpen5, setIsOpen5] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showWaitingModal, setShowWaitingModal] = useState(false);
  const [timerIncreased, setTimerIncreased] = useState(false);

  const openModal = () => {
    setIsOpen(!modalIsOpen);
  };
  const openModal2 = () => {
    setIsOpen2(!modalIsOpen2);
    setIsOpen(false);
  };
  const openModal3 = () => {
    setIsOpen3(!modalIsOpen3);
  };
  const openModal4 = () => {
    setIsOpen4(!modalIsOpen4);
  };
  const openModal5 = () => {
    setIsOpen5(!modalIsOpen4);
  };
  // const openModal4 = () => {
  //   setIsOpen3(!modalIsOpen3);
  //   setIsOpen2(false);
  //   setIsOpen(false);
  // };

  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal2 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal3 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal4 = () => {
    // references are now sync'd and can be accessed.
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  const closeModal2 = () => {
    setIsOpen(false);
  };
  const closeModal3 = () => {
    setIsOpen3(false);
  };
  const closeModal4 = () => {
    setIsOpen3(false);
  };



  const getOrderDetails = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/order/details/?order_id=${orderNumber}`
      );
      console.log("Trade order details:", res.data);

      if (res.data?.data) {
        const responseData = res.data.data;

        // Set time limit data
        if (responseData.time_limit) {
          setTimeLimit(responseData.time_limit);
        }

        // Set order data
        if (responseData.order_data) {
          const orderData = responseData.order_data;
          setTradeData({
            currency_from: orderData.currency_from,
            currency_to: orderData.currency_to,
            trade_amount: orderData.trade_amount,
            listing_data: orderData.listing_data,
            user_details: orderData.user_details,
            peer_details: orderData.peer_details,
            data: orderData.data,
            flag: orderData.flag,
            order_status: orderData.order_status,
          });

          // Set stepper to 5 if order status is completed
          if (orderData.order_status === "completed") {
            setActiveStep(5);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching trade details:", error);
    }
  };

  // Handle timer increase success - prevent waiting modal from showing again
  const handleTimerIncreased = () => {
    setTimerIncreased(true);
    getOrderDetails();
    // Reset the flag after a short delay to allow normal modal behavior later
    setTimeout(() => {
      setTimerIncreased(false);
    }, 3000);
  };

  useEffect(() => {
    getOrderDetails();
  }, [remflowEvent]);

  // Check if we should show waiting modal based on trade state
  useEffect(() => {
    // Show waiting modal if:
    // 1. Trade status is pending, OR
    // 2. Active step is 0 (not started), OR
    // 3. No active step set and no clear trade progression
    // 4. Only show for user (sender), not peer
    // 5. Don't show if timer was recently increased
    const isUserSender = tradeData?.flag === "user" || passedType === "user";
    const shouldShowWaiting =
      isUserSender &&
      !timerIncreased && (
        tradeData?.order_status === "pending" ||
        timeLimit?.status === "pending" ||
        (activeStep === 0 && tradeData && !timeLimit?.status) ||
        (activeStep === 0 && !btnNameUser && !btnNamePeer)
      );

    console.log("Waiting modal check:", {
      tradeStatus: tradeData?.order_status,
      timeLimitStatus: timeLimit?.status,
      activeStep,
      btnNameUser,
      btnNamePeer,
      isUserSender,
      timerIncreased,
      shouldShowWaiting,
      isMobile: typeof window !== "undefined" ? window.innerWidth <= 576 : false
    });

    setShowWaitingModal(shouldShowWaiting);
  }, [tradeData, timeLimit, activeStep, btnNameUser, btnNamePeer, passedType, timerIncreased]);

  // Note: Removed unused FormData objects that were being created on every render

  const handleCancelTrade = async () => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );

      if (res.status === 200) {
        toast.success(res.data.message || "Trade cancelled successfully. Redirecting to dashboard...");
        setShowWaitingModal(false);
        setTimeout(() => {
          router.push("/pages/dashboard");
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
      toast.error("Failed to cancel trade. Please try again.");
    }
  };
  const handleCancelTradePeer = async () => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );
      console.log("res", res);
      if (res.status === 200) {
        toast.success(res.data.message);
        // router.push("/pages/searchads");
      }
    } catch (error) {
      console.error("error", error);
    }
    // setTimeout(() => {
    //   router.push("/pages/searchads");
    // }, 1000);
  };

  const paymentRecivedByPeerApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendMoneyToUserApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-user/",
        payload
      );

      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromPeerToSender = async () => {
    if (peerTimeLine === "received_payment_by_peer") {
      paymentRecivedByPeerApi();
    }
    if (peerTimeLine === "send_to_user") {
      sendMoneyToUserApi();
    }

    if (peerTimeLine === "trade_complete") {
      setShowReviewModal(true);
    }
  };
  useEffect(() => {
    if (peerTimeLine === "trade_complete") {
      setShowReviewModal(true);
    }
  }, [peerTimeLine]);

  const sendMoneyToPeerApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const paymentRecivedByUserApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-user/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(Number(res.data.data.stepper));
        setTimeout(() => {
          setShowReviewModal(true);
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromSenderToPeer = async () => {
    if (userTimeLine === "pay_to_peer") {
      sendMoneyToPeerApi();
    }

    if (userTimeLine === "received_payment_by_user") {
      paymentRecivedByUserApi();
    }
    if (userTimeLine === "Trade_Completed") {
      setShowReviewModal(true);
    }
  };

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  if (!token && typeof window !== "undefined") {
    router.push("/sign/login");
  }

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "15px",
      width: "400px",
      zIndex: 100000,
    },
    overlay: {
      zIndex: 99999,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
  };

  // Flexbox centered modal styles for review modal
  const reviewModalStyles = {
    content: {
      position: "relative",
      top: "auto",
      left: "auto",
      right: "auto",
      bottom: "auto",
      border: "none",
      background: "white",
      borderRadius: "15px",
      outline: "none",
      padding: "0",
      margin: "0",
      transform: "none",
      width: "90%",
      maxWidth: "500px",
      maxHeight: "90vh",
      overflow: "auto",
    },
    overlay: {
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 99999,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: "20px",
    }
  };
  if (typeof window !== "undefined") {
    Modal.setAppElement("body");
  }

  // Create title for Layout component
  const tradeTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Trade</h1>
      <p className={styles.pageSubtitle}>
        Execute your trade securely • Order #{orderNumber}
      </p>
    </div>
  );

  return (
    <>
      <Layout title={tradeTitle}>
        <div className={styles.rightContainer}>
          <div className={styles.rightContainerWrapper}>
            <TradeTimer
              duration={timeLimit?.left_time_in_milliseconds}
              orderNumber={orderNumber}
              showModal={true}
              onTimeIncreased={handleTimerIncreased}
            />
            <TradeHeader
              tradeData={tradeData}
              orderNumber={orderNumber}
              timeLimit={timeLimit}
              passedType={passedType}
            />
            <div className={styles.bottomBoxWrapper}>
              <div className={styles.bottomLeftBox}>
                <div className={styles.tagArea}>
                  <div className={styles.senderTag}>
                    {tradeData?.flag === "user" || passedType === "user"
                      ? "You are the Sender"
                      : "You are the Peer"}
                  </div>
                  {(timeLimit?.status === "completed" || showReviewModal) && (
                    <div className={styles.completedTag}>Trade Completed</div>
                  )}
                  {timeLimit?.status === "expired" && (
                    <div className={styles.expiredTag}>Trade Expired</div>
                  )}
                </div>
                {timeLimit?.status === "completed" ||
                timeLimit?.status === "expired" ? (
                  ""
                ) : (
                  <TradeInstructions
                    activeStep={activeStep}
                    tradeData={tradeData}
                    timeLimit={timeLimit}
                    showReviewModal={showReviewModal}
                  />
                )}
                <div className={styles.progressBarArea}>
                  {/* <ProgressBar /> */}
                  <TradeStepper activeStep={activeStep} />
                </div>
                <PaymentInfo tradeData={tradeData} />
                <TradeActions
                  tradeData={tradeData}
                  timeLimit={timeLimit}
                  showReviewModal={showReviewModal}
                  btnNameUser={btnNameUser}
                  btnNamePeer={btnNamePeer}
                  sendPaymentFromSenderToPeer={sendPaymentFromSenderToPeer}
                  sendPaymentFromPeerToSender={sendPaymentFromPeerToSender}
                  modalIsOpen={modalIsOpen}
                  openModal={openModal}
                  closeModal={closeModal}
                  orderNumber={orderNumber}
                />
              </div>
              <div className={styles.bottomRightBox}>
                {/* <Chat /> */}

                <Chat Recipientname={username1} orderNumber={orderNumber} />

                {/* <Chat /> */}
              </div>
            </div>
          </div>
        </div>
      </Layout>

      {/* Modals */}
      <Modal
        className={styles.modalWrapper}
        isOpen={showReviewModal}
        onRequestClose={() => setShowReviewModal(false)}
        style={reviewModalStyles}
        contentLabel="Trade Review Modal"
      >
        <TradeReviewModal
          onClose={() => setShowReviewModal(false)}
          orderNumber={orderNumber}
          username={username1 || "Trader"}
        />
      </Modal>

      <WaitingModal
        showWaitingModal={showWaitingModal}
        orderNumber={orderNumber}
        tradeData={tradeData}
        handleCancelTrade={handleCancelTrade}
      />

      <Modal
        className={styles.modalWrapper}
        isOpen={modalIsOpen2}
        onAfterOpen={afterOpenModal2}
        onRequestClose={closeModal2}
        style={customStyles}
        contentLabel="Example Modal"
      >
        <DisputeTicket setIsOpen2={setIsOpen2} />
      </Modal>

      <Modal
        className={styles.modalWrapper}
        isOpen={modalIsOpen3}
        onAfterOpen={afterOpenModal3}
        onRequestClose={closeModal3}
        style={customStyles}
        contentLabel="Example Modal"
      >
        {/* Empty modal for future use */}
      </Modal>
    </>
  );
};

export default page;
